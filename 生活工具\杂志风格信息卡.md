核心哲学： 融合印刷品的饱和秩序感与高级玻璃拟态的未来感，打造水晶般通透的数字艺术品。

I. 视觉与布局
页面蓝图 (Blueprint): 严格遵循四段式结构：
页头 (Header): 专业刊头。
主内容区 (Main Body): 4+8 非对称网格。
4列侧边栏: 巨大、描边、空心的视觉锚点。
8列核心区: 紧凑布局的玻璃卡片。
中段分隔区 (Mid-Breaker): 全宽、风格化的玻璃区块。
深色页脚 (Dark Footer): 必须使用深色背景 (#1f2937)。
核心风格 (Core Style): 高级玻璃拟态 (Advanced Glassmorphism)。
页面背景: 克制的玻璃质感背景，带大范围柔和光晕。
内容容器:
模糊: backdrop-filter: blur(20px+)。
边框: 1px 锐利高光边框。
圆角: 减少 (0.5rem - 0.75rem)。
光影: 柔和 box-shadow + 微弱 inset 阴影。

II. 设计基因
字体系统 (Typography): 结构性反差。
中文: Noto Serif SC (大字号, 粗字重 700+)。
英文: Poppins (小字号, 轻字重 300)。
色彩系统 (Color): 辉光渐变 (Aura Gradients)。
选择一个明亮、柔和的主题色。
以半透明到透明的渐变形式，微妙地应用在玻璃卡片背景上。
元素 (Elements):
图标: Font Awesome 线稿风格 (light/regular)。
视觉流 (推荐): 背景中加入抽象、柔和的引导线。

III. 技术规格
交付物: 单一、自包含的 HTML5 文件。
技术栈: TailwindCSS, Google Fonts, Font Awesome (均通过 CDN)。
内容: 中文为主，不省略要点，无图表。
适配: 优先 1200x1600 宽高比，确保响应式。